import React, { useMemo } from "react";
import { useOffline } from "@/hooks/useOffline";
import { Action, ExternalData } from "@/interfaces";
import { ActionLocations, DisplayEnum } from "@/utils/Enums";

interface OfflineAwareMkdListTableProps {
  // Core table props
  table: string;
  tableRole?: string;
  defaultColumns?: any[];
  excludeColumns?: any[];
  columnModel?: any | null;
  processes?: any[];
  actions?: { [key: string]: Action };
  actionPostion?: ActionLocations[];
  actionId?: string;
  tableTitle?: string;
  tableSchema?: any[];
  hasFilter?: boolean;
  schemaFields?: any[];
  showPagination?: boolean;
  defaultFilter?: any[];
  refreshRef?: any | null;
  allowEditing?: boolean;
  allowSortColumns?: boolean;
  showSearch?: boolean;
  topClasses?: string;
  join?: any[];
  filterDisplays?: Array<
    | DisplayEnum.ROWS
    | DisplayEnum.COLUMNS
    | DisplayEnum.SORT
    | DisplayEnum.FILTER
  >;
  resetFilters?: any | null;
  defaultPageSize?: number;
  searchFilter?: any[];
  onReady?: (data: Array<Record<any, any>>) => void;
  maxHeight?: string | null;
  rawFilter?: any[];
  externalData: ExternalData;
  noDataComponent?: {
    use: boolean;
    component: JSX.Element;
  };
  useImage?: boolean;
  canChangeLimit?: boolean;
  selectedItemsRef?: any | null;
  useDefaultColumns?: boolean;
  showYScrollbar?: boolean;
  showXScrollbar?: boolean;
  showScrollbar?: boolean;

  // Offline-specific props
  showOfflineIndicators?: boolean;
  offlineRowClassName?: string;
  enableOfflineActions?: boolean;
}

/**
 * Offline-aware version of MkdListTable that shows offline status and handles offline data
 */
export const OfflineAwareMkdListTable: React.FC<
  OfflineAwareMkdListTableProps
> = ({
  showOfflineIndicators = true,
  offlineRowClassName = "bg-yellow-50 border-l-4 border-l-yellow-400",
  enableOfflineActions = true,
  externalData,
  actions,
  ...props
}) => {
  const { state } = useOffline();
  const { networkStatus, queueStats } = state;

  // Enhance external data to show offline status
  const enhancedExternalData = useMemo(() => {
    if (!externalData || !showOfflineIndicators) {
      return externalData;
    }

    return {
      ...externalData,
      data: externalData.data?.map((item: any) => ({
        ...item,
        _isOffline: item._offline || item._cached || false,
        _isPending: item._requestId && queueStats.total > 0,
      })),
    };
  }, [externalData, showOfflineIndicators, queueStats.total]);

  // Enhance actions to handle offline scenarios
  const enhancedActions = useMemo(() => {
    if (!enableOfflineActions || !actions) {
      return actions;
    }

    return Object.entries(actions).map(([action, config]) => ({
      ...action,
      onClick: async (row: any, ...args: any[]) => {
        // If offline and this is a destructive action, show warning
        if (
          !networkStatus.isOnline &&
          ["delete", "remove"].includes(action.name?.toLowerCase() || "")
        ) {
          const confirmed = window.confirm(
            "You are offline. This action will be queued and executed when you come back online. Continue?"
          );
          if (!confirmed) return;
        }

        // Call original action
        if (action.onClick) {
          return action.onClick(row, ...args);
        }
      },
    }));
  }, [actions, enableOfflineActions, networkStatus.isOnline]);

  // Custom row className to highlight offline rows
  const getRowClassName = (row: any, index: number) => {
    let className = "";

    // Apply original row className if provided
    if (props.rowClassName) {
      if (typeof props.rowClassName === "function") {
        className = props.rowClassName(row, index);
      } else {
        className = props.rowClassName;
      }
    }

    // Add offline styling
    if (showOfflineIndicators && row._isOffline) {
      className += ` ${offlineRowClassName}`;
    }

    return className;
  };

  return (
    <div className="relative">
      {/* Offline banner */}
      {!networkStatus.isOnline && (
        <div className="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-3 mb-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg
                className="h-5 w-5 text-yellow-400"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm">
                You are currently offline. Data shown may be cached.
                {queueStats.total > 0 &&
                  ` ${queueStats.total} changes are pending sync.`}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Enhanced table */}
      <MkdListTableV2
        {...props}
        externalData={enhancedExternalData}
        columns={enhancedColumns}
        actions={enhancedActions}
        rowClassName={getRowClassName}
      />

      {/* Sync status footer */}
      {queueStats.total > 0 && (
        <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
              <span className="text-sm text-blue-700">
                {queueStats.total} changes pending sync
              </span>
            </div>
            <div className="text-xs text-blue-600">
              High: {queueStats.byPriority.high}, Medium:{" "}
              {queueStats.byPriority.medium}, Low: {queueStats.byPriority.low}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default OfflineAwareMkdListTable;
